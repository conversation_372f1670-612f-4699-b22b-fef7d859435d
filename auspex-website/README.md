# Auspex Records Website

The official website for Auspex Records, an independent psychedelic trance music label committed to curating and cultivating future-facing sound. Built with modern web technologies and deployed on AWS with full CI/CD automation.

## 🌐 Live Environments

| Environment | URL | Purpose |
|-------------|-----|---------|
| **Production** | [auspexrecords.com](https://auspexrecords.com) | Live public website |
| **Staging** | [stage.auspexrecords.com](https://stage.auspexrecords.com) | Testing and preview |

## 🛠️ Tech Stack

### Frontend
*   **[Next.js 14](https://nextjs.org/)** - React framework with App Router and static generation
*   **[React 18](https://reactjs.org/)** - Component-based UI with modern hooks and patterns
*   **[TypeScript](https://www.typescriptlang.org/)** - Type-safe development with full IntelliSense
*   **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework for rapid styling
*   **[ShadCN UI](https://ui.shadcn.com/)** - Accessible and customizable component library
*   **[Framer Motion](https://www.framer.com/motion/)** - Smooth animations and micro-interactions
*   **[Lucide React](https://lucide.dev/)** - Modern, consistent icon library

### Infrastructure & Deployment
*   **[AWS S3](https://aws.amazon.com/s3/)** - Static website hosting and asset storage
*   **[AWS CloudFront](https://aws.amazon.com/cloudfront/)** - Global CDN with edge caching
*   **[AWS Route53](https://aws.amazon.com/route53/)** - DNS management and domain routing
*   **[AWS Certificate Manager](https://aws.amazon.com/certificate-manager/)** - SSL/TLS certificates
*   **[Terraform](https://www.terraform.io/)** - Infrastructure as Code with modular architecture

## 🏗️ Architecture

### Application Architecture
*   **Frontend**: Next.js React application with static site generation (SSG)
*   **Data Layer**: Static TypeScript data files (`src/lib/data.ts`) - no database required
*   **Asset Storage**: AWS S3 buckets for downloads, album art, and static assets
*   **Content Delivery**: AWS CloudFront for global edge caching and performance
*   **Domain Management**: AWS Route53 for DNS with SSL certificates from ACM

### Infrastructure Architecture
*   **Modular Terraform**: Organized into environments (`staging`, `prod`) and shared modules
*   **Environment Isolation**: Complete separation between staging and production
*   **State Management**: Terraform state stored in S3 with DynamoDB locking
*   **Automated Deployment**: Environment-specific build and deploy scripts

## 🚀 Getting Started

### Prerequisites
- **Node.js 18+** - [Download here](https://nodejs.org/)
- **AWS CLI** - [Installation guide](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
- **Terraform 1.2+** - [Download here](https://www.terraform.io/downloads)
- **Git** - For version control

### Local Development Setup

1.  **Clone and navigate to the project:**
    ```bash
    git clone <repository-url>
    cd auspex/auspex-website
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Start the development server:**
    ```bash
    npm run dev
    ```

4.  **Open in browser:**
    The application will be available at [http://localhost:3000](http://localhost:3000)

### Development Commands

```bash
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run start        # Start production server locally
npm run lint         # Run ESLint for code quality
npm run typecheck    # Run TypeScript type checking
```

## ✨ Features

### Core Functionality
-   **🎵 Music Releases** - Browse releases with album art, track listings, and streaming platform links
-   **🎧 Live Performances** - Watch recorded live sets and DJ performances
-   **📱 Responsive Design** - Mobile-first design with smooth animations and glass morphism effects
-   **🔍 Year-based Filtering** - Browse releases by year with dynamic filtering
-   **⚡ Fast Loading** - Static site generation with global CDN for optimal performance

### Integrations
-   **🎬 YouTube Integration** - Embedded player for track previews and live set viewing
-   **🎼 Multi-Platform Links** - Direct links to Spotify, Bandcamp, SoundCloud, Apple Music
-   **💾 Multi-Format Downloads** - Direct S3 downloads in MP3, FLAC, AAC, WAV, and other formats
-   **🎨 Dynamic UI** - Animated backgrounds, glass morphism effects, and smooth transitions

### Technical Features
-   **🔒 SSL/HTTPS** - Secure connections with AWS Certificate Manager
-   **🌍 Global CDN** - CloudFront edge locations for worldwide performance
-   **📊 Static Data** - No database required, all data in TypeScript files
-   **🔄 Automated Deployment** - Environment-specific build and deploy scripts

## 🚀 Deployment

### Environment-Specific Deployment

#### Staging Deployment
```bash
# Build and deploy to staging environment
npm run deploy:staging
```
This command:
1. Builds the site with staging configuration
2. Syncs files to staging S3 bucket
3. Invalidates CloudFront cache
4. Site available at: https://stage.auspexrecords.com

#### Production Deployment
```bash
# Build and deploy to production environment
npm run deploy:prod
```
This command:
1. Builds the site with production configuration
2. Syncs files to production S3 bucket
3. Invalidates CloudFront cache
4. Site available at: https://auspexrecords.com

### Manual Deployment Steps

The automated deployment scripts (`npm run deploy:staging` and `npm run deploy:prod`) perform these steps automatically. Manual deployment is useful for troubleshooting or custom deployments:

```bash
# 1. Build the static site for your target environment
npm run build:staging  # or npm run build:prod

# 2. Navigate to the corresponding Terraform environment
cd terraform/environments/staging  # or prod

# 3. Deploy to S3 and invalidate CloudFront cache
aws s3 sync ../../../out/ s3://$(terraform output -raw s3_bucket_name) --delete
aws cloudfront create-invalidation --distribution-id $(terraform output -raw cloudfront_distribution_id) --paths '/*'
```

**Why these steps are needed:**
- **S3 sync**: Uploads the built static files to the S3 bucket that hosts the website
- **CloudFront invalidation**: Clears the CDN cache so users see the updated content immediately
- **Terraform outputs**: Dynamically retrieves the correct bucket name and distribution ID for the environment

## 🏗️ Infrastructure Setup

### Initial Infrastructure Deployment

#### 1. Bootstrap Terraform State Management
```bash
cd terraform/bootstrap
terraform init
terraform apply
```

#### 2. Deploy Staging Environment
```bash
cd ../environments/staging
terraform init
terraform apply
```

#### 3. Deploy Production Environment
```bash
cd ../environments/prod
terraform init
terraform apply
```

### Terraform Module Structure
```
terraform/
├── bootstrap/              # State management setup
├── environments/
│   ├── staging/            # Staging environment config
│   └── prod/               # Production environment config
└── modules/
    ├── website/            # S3 + CloudFront + Route53
    └── domains/            # Domain and certificate management
```

## 📊 Data Structure

The website uses a static data approach for optimal performance:

-   **Release Data:** Managed in `/src/lib/data.ts` with comprehensive type safety
-   **Type Definitions:** Full TypeScript interfaces in `/src/lib/types.ts`
-   **S3 Integration:** Direct downloads from `auspex-records-releases` S3 bucket
-   **Media Assets:** Album covers and logos stored in `/public/` directory
-   **No Database:** All data is statically compiled for maximum performance

## 📁 Project Structure

```
auspex-website/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── page.tsx           # Landing page
│   │   ├── releases/          # Music releases page
│   │   ├── live-sets/         # Live performances page
│   │   └── layout.tsx         # Root layout component
│   ├── components/            # React components
│   │   ├── ui/               # ShadCN UI components
│   │   ├── releases/         # Release-specific components
│   │   ├── sets/             # Live set components
│   │   └── layout/           # Layout components (header, footer)
│   └── lib/                  # Utilities and data
│       ├── data.ts           # Static release and set data
│       ├── types.ts          # TypeScript type definitions
│       └── utils.ts          # Utility functions
├── public/                   # Static assets
│   ├── logos/               # Brand logos and icons
│   ├── album-art/           # Release cover artwork
│   └── wallpapers/          # Background images
├── terraform/               # Infrastructure as Code
│   ├── bootstrap/           # Terraform state setup
│   ├── environments/        # Environment-specific configs
│   └── modules/             # Reusable Terraform modules
├── out/                     # Built static files (generated)
├── package.json             # Dependencies and scripts
├── next.config.ts           # Next.js configuration
├── tailwind.config.ts       # Tailwind CSS configuration
└── tsconfig.json            # TypeScript configuration
```

## 🤝 Contributing

### Development Workflow

1. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes:**
   - Follow the existing code style and patterns
   - Update TypeScript types if adding new data structures
   - Test your changes locally with `npm run dev`

3. **Test thoroughly:**
   ```bash
   npm run lint        # Check code quality
   npm run typecheck   # Verify TypeScript types
   npm run build       # Test production build
   ```

4. **Deploy to staging for testing:**
   ```bash
   npm run deploy:staging
   ```

5. **Create a pull request:**
   - Describe your changes clearly
   - Include screenshots for UI changes
   - Mention any breaking changes

### Adding New Releases

1. **Add release data** to `src/lib/data.ts`:
   ```typescript
   {
     id: "unique-release-id",
     title: "Album Title",
     artist: "Artist Name",
     year: 2024,
     coverArt: "/album-art/artist-album.jpg",
     // ... other fields
   }
   ```

2. **Add cover art** to `public/album-art/`

3. **Update types** in `src/lib/types.ts` if needed

4. **Test locally** and deploy to staging

### Code Style Guidelines

- **TypeScript**: Use strict typing, avoid `any`
- **Components**: Use functional components with hooks
- **Styling**: Use Tailwind CSS classes, avoid custom CSS when possible
- **File naming**: Use kebab-case for files, PascalCase for components
- **Imports**: Use absolute imports from `@/` for src directory

### Environment Configuration

The project uses build-time environment variables for different deployment targets:

- **Staging builds**: `npm run build:staging` - No additional configuration needed
- **Production builds**: `npm run build:prod` - No additional configuration needed
- **Local development**: `npm run dev` - Uses development defaults

No `.env` files are required as all configuration is handled through the build scripts.

## 🔧 Troubleshooting

### Common Issues

**Build failures:**
- Check TypeScript errors: `npm run typecheck`
- Verify all imports are correct
- Ensure all required environment variables are set

**Deployment failures:**
- Verify AWS credentials: `aws sts get-caller-identity`
- Check Terraform state: `terraform plan` in environment directory
- Ensure S3 bucket and CloudFront distribution exist

**Local development issues:**
- Clear Next.js cache: `rm -rf .next`
- Reinstall dependencies: `rm -rf node_modules && npm install`
- Check Node.js version: `node --version` (should be 18+)

### Getting Help

- Check the [main project README](../README.md) for overall architecture
- Review [Terraform documentation](./terraform/README.md) for infrastructure issues
- Look at existing components for patterns and examples
