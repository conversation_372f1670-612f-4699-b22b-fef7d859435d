# Auspex Records Label Tools

This directory contains automation tools for processing and managing music releases for Auspex Records. These Python scripts handle the complete workflow from raw audio files to web-ready releases with multiple formats and promotional videos.

## 🚀 Quick Start

### Prerequisites
- **Python 3.12+**
- **FFmpeg** with full codec support
- **AWS CLI** configured with appropriate permissions
- **Sufficient disk space** for temporary processing

### Setup
```bash
# Navigate to tools directory
cd tools

# Install Python dependencies
pip install -r requirements.txt

# Verify FFmpeg installation
ffmpeg -version

# Verify AWS configuration
aws sts get-caller-identity
```

## 📋 Available Tools

## [`convert_s3_codecs.py`](convert_s3_codecs.py)

### Overview

An automated audio processing pipeline that converts high-quality WAV files into multiple audio formats and makes them available for download on the Auspex Records website. The script handles the entire workflow from raw audio files to web-ready download packages.

### How It Works

The script operates as a **differential processing system** that:

1. **Compares S3 buckets** to identify new releases that need processing
2. **Downloads raw audio files** from the preprocessing bucket
3. **Converts audio** to multiple formats using FFmpeg
4. **Packages releases** into format-specific ZIP files
5. **Uploads processed releases** to the public releases bucket

### Input Requirements

#### S3 Bucket Structure

**Preprocessing Bucket (`auspex-records-preprocess`):**
```
auspex-records-preprocess/
├── Artist Name - Album Title/
│   ├── 01 - Track Name.wav          # High-quality WAV files (required)
│   ├── 02 - Another Track.wav
│   ├── cover.jpg                    # Album artwork (optional)
│   └── liner-notes.pdf              # Additional files (optional)
└── Another Artist - Another Album/
    ├── 01 - Song One.wav
    └── cover.png
```

#### File Requirements

- **Audio Files**: Must be in WAV format (`.wav` extension)
- **Quality**: Recommend 24-bit/48kHz or higher for best results
- **Naming**: Use consistent naming convention: `## - Track Name.wav`
- **Additional Files**: Cover art, liner notes, etc. will be copied to all format folders

### Output Structure

**Releases Bucket (`auspex-records-releases`):**
```
auspex-records-releases/
└── Artist Name - Album Title/
    ├── Artist Name - Album Title - MP3 320.zip
    ├── Artist Name - Album Title - MP3 V0.zip
    ├── Artist Name - Album Title - FLAC.zip
    ├── Artist Name - Album Title - AAC.zip
    ├── Artist Name - Album Title - Ogg Vorbis.zip
    ├── Artist Name - Album Title - ALAC.zip
    ├── Artist Name - Album Title - WAV.zip
    └── Artist Name - Album Title - AIFF.zip
```

Each ZIP file contains:
- All tracks in the specified format
- Cover art and additional files (copied from input)
- Proper file extensions for each format

### Audio Formats & Quality Settings

| Format | Quality Setting | File Extension | Use Case |
|--------|----------------|----------------|----------|
| **MP3 320** | 320 kbps CBR | `.mp3` | High-quality lossy, universal compatibility |
| **MP3 V0** | Variable bitrate | `.mp3` | Efficient lossy, smaller file sizes |
| **FLAC** | Lossless | `.flac` | Audiophile quality, open source |
| **AAC** | VBR quality 0 | `.aac` | Apple ecosystem, efficient compression |
| **Ogg Vorbis** | VBR quality 0 | `.oga` | Open source alternative to MP3 |
| **ALAC** | Lossless | `.m4a` | Apple lossless format |
| **WAV** | Uncompressed | `.wav` | Original quality, universal |
| **AIFF** | Uncompressed | `.aiff` | Apple uncompressed format |

### Prerequisites & Setup

#### System Requirements
- **Python 3.12+**
- **FFmpeg** (with all codec support)
- **AWS CLI** configured with appropriate permissions
- **Sufficient disk space** for temporary processing (estimate 2-3GB per album)

#### Installation Steps

1. **Install Python dependencies:**
   ```bash
   pip install boto3
   ```

2. **Install FFmpeg:**
   ```bash
   # macOS
   brew install ffmpeg

   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg

   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

3. **Configure AWS credentials:**
   ```bash
   aws configure
   # Enter your AWS Access Key ID, Secret Access Key, and region
   ```

4. **Create S3 buckets:**
   ```bash
   aws s3 mb s3://auspex-records-preprocess
   aws s3 mb s3://auspex-records-releases
   ```

#### Required AWS Permissions

Your AWS user/role needs the following permissions:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket",
                "s3:GetObject",
                "s3:PutObject"
            ],
            "Resource": [
                "arn:aws:s3:::auspex-records-preprocess",
                "arn:aws:s3:::auspex-records-preprocess/*",
                "arn:aws:s3:::auspex-records-releases",
                "arn:aws:s3:::auspex-records-releases/*"
            ]
        }
    ]
}

### Usage

#### Basic Usage
```bash
cd tools
python convert_s3_codecs.py
```

#### What Happens When You Run It

1. **Bucket Comparison**: Script compares preprocessing and releases buckets
2. **Progress Output**: Shows which albums will be processed
3. **Download Phase**: Downloads new albums from preprocessing bucket
4. **Conversion Phase**: Converts each WAV file to all 8 formats (parallel processing)
5. **Packaging Phase**: Creates ZIP files for each format
6. **Upload Phase**: Uploads all ZIP files to releases bucket
7. **Cleanup**: Removes temporary local files

#### Example Output
```
Directories to process: [
  "Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/",
  "Oak Project - Reflections/"
]
Processing: Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/
Downloading Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/01 - Its all how you look at it.wav...
Downloading Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/cover.jpg...
Converting 01 - Its all how you look at it...
Converting 02 - Lentamente...
... 01 - Its all how you look at it done.
... 02 - Lentamente done.
Uploading tmp/convert_s3_codecs/output/Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/Paranoiac - The Pots Of My Heart Are Full Of Your Seeds - MP3 320.zip...
Uploading tmp/convert_s3_codecs/output/Paranoiac - The Pots Of My Heart Are Full Of Your Seeds/Paranoiac - The Pots Of My Heart Are Full Of Your Seeds - FLAC.zip...
...
```

### Workflow Integration

#### For New Releases

1. **Prepare Audio Files**:
   - Master your tracks to high-quality WAV files
   - Use consistent naming: `01 - Track Name.wav`
   - Include cover art and any additional files

2. **Upload to Preprocessing Bucket**:
   ```bash
   aws s3 sync "local/album/folder" s3://auspex-records-preprocess/"Artist - Album Title"/
   ```

3. **Run Processing Script**:
   ```bash
   python convert_s3_codecs.py
   ```

4. **Verify Results**:
   - Check that all ZIP files were created in releases bucket
   - Test download links on website
   - Verify audio quality of converted files

#### Integration with Website

The processed files integrate directly with the Auspex Records website:

- **Download URLs**: `https://auspex-records-releases.s3.us-west-1.amazonaws.com/{Artist}+-+{Album}/{Artist}+-+{Album}+-+{Format}.zip`
- **Format Selection**: Website download modal uses these exact format names
- **Automatic Discovery**: New releases appear automatically once ZIP files are uploaded

### Performance & Optimization

#### Processing Time
- **Per Track**: ~2-3 minutes for all 8 formats (parallel processing)
- **Per Album**: Depends on track count (4 tracks ≈ 8-12 minutes)
- **Bottlenecks**: FFmpeg conversion, S3 upload bandwidth

#### Resource Usage
- **CPU**: High during conversion (uses all available cores)
- **Memory**: ~500MB-1GB during processing
- **Disk**: 2-3GB temporary space per album
- **Network**: Upload bandwidth dependent

#### Optimization Tips
- **Run during off-peak hours** for faster S3 transfers
- **Ensure sufficient disk space** before processing large batches
- **Monitor AWS costs** - S3 transfer and storage fees apply
- **Use SSD storage** for faster temporary file operations

### Troubleshooting

#### Common Issues

**"No such file or directory" errors:**
- Ensure AWS credentials are configured: `aws configure list`
- Verify bucket names and permissions
- Check that buckets exist: `aws s3 ls`

**FFmpeg conversion failures:**
- Verify FFmpeg installation: `ffmpeg -version`
- Check input WAV file integrity
- Ensure sufficient disk space

**S3 upload failures:**
- Check AWS credentials and permissions
- Verify internet connectivity
- Monitor AWS service status

**Empty processing list:**
- Confirms no new albums need processing
- Check preprocessing bucket contents: `aws s3 ls s3://auspex-records-preprocess/`
- Verify album folder structure matches expected format

#### Debug Mode
Add debug output by modifying the script:
```python
# Add at top of main() function
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Configuration

#### Customizing Bucket Names
Edit the constants at the top of `convert_s3_codecs.py`:
```python
S3_PREPROCESS_BUCKET = "your-preprocess-bucket"
S3_RELEASES_BUCKET = "your-releases-bucket"
```

#### Adding New Audio Formats
To add additional formats, modify the `convert_commands` list in the `convert_file()` function:
```python
# Example: Add OGG format
[
    f"ffmpeg -i \"{input_path}\" -codec:a libvorbis -q:a 5 \"{output_path}.ogg\"",
    "OGG"
]
```

#### Adjusting Quality Settings
Modify the FFmpeg parameters in `convert_commands`:
- **MP3 bitrate**: Change `-b:a 320k` to desired bitrate
- **FLAC compression**: Add `-compression_level 8` for maximum compression
- **AAC quality**: Adjust `-q:a 0` (0=highest, 5=lowest)

### Security Considerations

- **AWS Credentials**: Never commit credentials to version control
- **Bucket Permissions**: Use least-privilege access policies
- **Public Access**: Releases bucket should allow public read access for downloads
- **Preprocessing Bucket**: Should be private, only accessible by processing script

### Monitoring & Maintenance

#### Regular Tasks
- **Monitor S3 costs** and usage patterns
- **Clean up old preprocessing files** after successful processing
- **Verify backup procedures** for both buckets
- **Update FFmpeg** periodically for security and performance

#### Automation Opportunities
- **Scheduled Processing**: Run script via cron/scheduled task
- **Webhook Integration**: Trigger processing on new uploads
- **Notification System**: Alert on processing completion/failures
- **Quality Assurance**: Automated audio quality verification

---

## [`create_youtube_video.py`](create_youtube_video.py)

### Overview

A powerful video creation tool that generates high-quality 4K YouTube videos from audio tracks and static images. Perfect for music labels to create professional-looking videos for streaming platforms, social media, and promotional content.

### How It Works

The script combines audio files with static images to create **4K resolution videos** (3840x2160) optimized for YouTube and other video platforms. It uses FFmpeg to:

1. **Loop a static image** for the duration of the audio
2. **Encode video** in high-quality H.264 format
3. **Process audio** with AAC codec at 320kbps
4. **Output 4K videos** ready for upload to YouTube

### Input Requirements

#### Audio Files
**Supported Formats:**
- `.mp3` - Most common format
- `.wav` - High quality uncompressed
- `.flac` - Lossless compression
- `.m4a` - Apple/AAC format
- `.ogg` - Ogg Vorbis format
- `.aac` - Raw AAC format

**Quality Recommendations:**
- **Minimum**: 320kbps MP3 or equivalent
- **Recommended**: FLAC or high-bitrate formats for best results
- **Duration**: Any length (script automatically matches video duration to audio)

#### Image Files
**Supported Formats:**
- `.jpg` / `.jpeg` - Most common, good compression
- `.png` - Supports transparency, larger files
- `.bmp` - Uncompressed bitmap

**Quality Requirements:**
- **Minimum Resolution**: 1920x1080 (HD)
- **Recommended**: 3840x2160 (4K) or higher
- **Aspect Ratio**: 16:9 for best YouTube compatibility
- **File Size**: No strict limit, but larger images take longer to process

### Usage Modes

#### Mode 1: Single Image for All Videos
Use the same image (like album artwork) for all tracks:

```bash
python create_youtube_video.py \
    --audio-folder "path/to/audio/files" \
    --image "path/to/album-cover.jpg" \
    --output-folder "youtube_videos"
```

#### Mode 2: Matching Images for Each Track
Use different images for each track (images must match audio filenames):

```bash
python create_youtube_video.py \
    --audio-folder "path/to/audio/files" \
    --image "path/to/default-image.jpg" \
    --image-folder "path/to/images" \
    --output-folder "youtube_videos"
```

### Command Line Arguments

| Argument | Required | Description | Example |
|----------|----------|-------------|---------|
| `--audio-folder` | ✅ | Path to folder containing audio files | `./audio/album` |
| `--image` | ✅ | Default image file for all videos | `./cover.jpg` |
| `--output-folder` | ❌ | Output directory (default: `youtube_videos`) | `./videos` |
| `--image-folder` | ❌ | Folder with images matching audio filenames | `./images` |

### Input/Output Examples

#### Example 1: Album Release Videos

**Input Structure:**
```
my-album/
├── audio/
│   ├── 01 - Track One.mp3
│   ├── 02 - Track Two.mp3
│   └── 03 - Track Three.mp3
├── cover.jpg
└── images/
    ├── 01 - Track One.jpg    # Optional: track-specific image
    └── 02 - Track Two.png    # Optional: track-specific image
```

**Command:**
```bash
python create_youtube_video.py \
    --audio-folder "my-album/audio" \
    --image "my-album/cover.jpg" \
    --image-folder "my-album/images" \
    --output-folder "my-album/videos"
```

**Output Structure:**
```
my-album/videos/
├── 01 - Track One.mp4      # Uses 01 - Track One.jpg
├── 02 - Track Two.mp4      # Uses 02 - Track Two.png
└── 03 - Track Three.mp4    # Uses cover.jpg (no matching image)
```

#### Example 2: Simple Album Videos

**Input Structure:**
```
simple-album/
├── audio/
│   ├── Song A.wav
│   ├── Song B.flac
│   └── Song C.mp3
└── album-artwork.png
```

**Command:**
```bash
python create_youtube_video.py \
    --audio-folder "simple-album/audio" \
    --image "simple-album/album-artwork.png"
```

**Output Structure:**
```
youtube_videos/              # Default output folder
├── Song A.mp4              # All use album-artwork.png
├── Song B.mp4
└── Song C.mp4
```

### Technical Specifications

#### Video Output Settings
- **Resolution**: 4K (3840x2160 pixels)
- **Frame Rate**: 24 fps (standard for YouTube)
- **Video Codec**: H.264 (libx264)
- **Quality**: CRF 18 (high quality, range 0-51)
- **Preset**: veryslow (maximum quality encoding)
- **Pixel Format**: yuv420p (universal compatibility)

#### Audio Output Settings
- **Codec**: AAC (best compatibility)
- **Bitrate**: 320kbps (high quality)
- **Sample Rate**: Preserved from source
- **Channels**: Preserved from source

#### File Sizes (Approximate)
- **3-minute track**: ~150-300MB per video
- **5-minute track**: ~250-500MB per video
- **10-minute track**: ~500MB-1GB per video

*File sizes depend on image complexity and audio content*

### Prerequisites & Setup

#### System Requirements
- **Python 3.6+**
- **FFmpeg** with H.264 and AAC support
- **Sufficient disk space** (videos are large files)
- **Processing power** (encoding is CPU-intensive)

#### Installation Steps

1. **Install FFmpeg:**
   ```bash
   # macOS
   brew install ffmpeg

   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg

   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

2. **Verify FFmpeg installation:**
   ```bash
   ffmpeg -version
   ffprobe -version
   ```

3. **No additional Python packages required** (uses only standard library)

### Performance & Optimization

#### Processing Time
- **Per minute of audio**: ~2-5 minutes processing time
- **Factors affecting speed**:
  - CPU performance (more cores = faster)
  - Image resolution (4K images process slower)
  - Audio length (longer = more processing)
  - Storage speed (SSD recommended)

#### Resource Usage
- **CPU**: High usage during encoding (uses multiple cores)
- **Memory**: ~1-2GB per video being processed
- **Disk**: Temporary space needed (2-3x final video size)
- **Storage**: Final videos are large (see file sizes above)

#### Optimization Tips
- **Use SSD storage** for faster I/O operations
- **Process during off-peak hours** for system performance
- **Batch process** multiple videos overnight
- **Monitor disk space** - videos consume significant storage
- **Use appropriate image sizes** - don't use 8K images for 4K output

### Quality Settings Explained

#### Video Quality (CRF Values)
- **CRF 18** (default): High quality, larger files
- **CRF 23**: Good quality, balanced file size
- **CRF 28**: Lower quality, smaller files

To change quality, modify the script:
```python
'-crf', '23',  # Change from '18' to '23' for smaller files
```

#### Encoding Presets
- **veryslow** (default): Best quality, slowest encoding
- **slow**: Good quality, faster encoding
- **medium**: Balanced quality/speed
- **fast**: Lower quality, much faster

### Workflow Integration

#### For Music Releases

1. **Prepare Assets:**
   - Export high-quality audio files
   - Create or obtain high-resolution artwork
   - Organize files in folders

2. **Create Videos:**
   ```bash
   python create_youtube_video.py \
       --audio-folder "release/audio" \
       --image "release/cover.jpg" \
       --output-folder "release/videos"
   ```

3. **Upload to Platforms:**
   - YouTube: Direct upload of MP4 files
   - Instagram: May need format conversion
   - TikTok: Consider creating shorter versions

#### Integration with Auspex Records Website

The generated videos can be used for:
- **YouTube embedding** on release pages
- **Social media promotion** across platforms
- **Streaming platform submissions** (some accept video content)
- **Press kit materials** for media outlets

### Troubleshooting

#### Common Issues

**"ffmpeg not found" error:**
- Install FFmpeg: `brew install ffmpeg` (macOS) or equivalent
- Verify installation: `ffmpeg -version`
- Check PATH environment variable

**"No supported audio files found":**
- Verify audio files have supported extensions
- Check file permissions and accessibility
- Ensure audio folder path is correct

**Video creation fails:**
- Check available disk space (need 2-3x final video size)
- Verify image file is not corrupted
- Test with shorter audio file first

**Poor video quality:**
- Use higher resolution source images (4K recommended)
- Verify image aspect ratio (16:9 ideal)
- Check audio quality of source files

#### Debug Mode
Add verbose output by modifying FFmpeg command:
```python
# Add these flags to the cmd list in create_video()
'-v', 'verbose',  # Add after 'ffmpeg'
```

### Advanced Usage

#### Custom Video Settings
Modify the script for different requirements:

```python
# For 1080p videos instead of 4K
'-vf', 'scale=1920:1080',

# For different frame rates
'-framerate', '30',  # 30fps instead of 24fps

# For different audio quality
'-b:a', '192k',  # Lower bitrate for smaller files
```

#### Batch Processing Script
Create a wrapper script for multiple albums:

```bash
#!/bin/bash
for album in */; do
    python create_youtube_video.py \
        --audio-folder "$album/audio" \
        --image "$album/cover.jpg" \
        --output-folder "$album/videos"
done
```

### Best Practices

#### File Organization
- **Consistent naming**: Use clear, consistent file naming conventions
- **Folder structure**: Organize by album/release for easy management
- **Backup strategy**: Videos are time-consuming to recreate

#### Quality Control
- **Test first**: Process one track before batch processing
- **Verify output**: Check video quality and audio sync
- **Monitor resources**: Ensure sufficient disk space and processing power

#### YouTube Optimization
- **Thumbnails**: Create custom thumbnails (videos use static images)
- **Metadata**: Add proper titles, descriptions, and tags
- **Playlists**: Organize videos into album/release playlists
- **End screens**: Consider adding end screens for engagement
```