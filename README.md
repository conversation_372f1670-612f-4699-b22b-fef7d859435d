# Auspex Records

A complete web platform for Auspex Records, an independent psychedelic trance music label. This project includes a modern Next.js website with AWS infrastructure and automation tools for music release processing.

## 🌐 Live Sites

| Environment | URL | Status |
|-------------|-----|--------|
| **Production** | [auspexrecords.com](https://auspexrecords.com) | ✅ Live |
| **Staging** | [stage.auspexrecords.com](https://stage.auspexrecords.com) | ✅ Live |

## 📁 Project Structure

```
auspex/
├── auspex-website/         # Main website application and infrastructure
│   ├── src/               # Next.js React frontend source code
│   ├── terraform/         # AWS Infrastructure as Code (modular structure)
│   ├── public/            # Static assets (logos, album art)
│   └── out/               # Built static website files
└── tools/                 # Automation tools for music processing
    ├── convert_s3_codecs.py    # Audio format conversion pipeline
    ├── create_youtube_video.py # Video generation for releases
    └── requirements.txt        # Python dependencies
```

### auspex-website/
The main website application built with Next.js and deployed on AWS. Features a modern, responsive design showcasing music releases, live performances, and streaming platform integrations. Infrastructure is managed with Terraform using a modular approach with separate staging and production environments.

### tools/
Automation scripts for processing music releases. Includes tools for converting audio files to multiple formats, creating YouTube videos from audio tracks, and managing S3-based distribution workflows.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- AWS CLI configured with appropriate permissions
- Terraform 1.2+
- Python 3.12+ (for tools)
- FFmpeg (for audio/video processing tools)

### Development Setup
```bash
# Setup website
cd auspex-website
npm install

# Start development server
npm run dev
```

### Deployment
```bash
# Deploy to staging
npm run deploy:staging

# Deploy to production (after testing staging)
npm run deploy:prod
```

## 🏗️ Architecture

### Frontend
- **Next.js 14** with App Router and TypeScript
- **Tailwind CSS** with ShadCN UI components
- **Framer Motion** for animations
- **Static data** from TypeScript files (no database required)

### Infrastructure (AWS)
- **S3** for static website hosting and asset storage
- **CloudFront** for global CDN and caching
- **Route53** for DNS management
- **ACM** for SSL certificates
- **Terraform** for Infrastructure as Code

### Infrastructure Management
- **Modular Terraform** structure with environments and shared modules
- **Multi-environment** setup (staging/production)
- **Automated state management** with S3 backend and DynamoDB locking
- **Environment-specific** build and deployment scripts

## 📋 Available Commands

### Website Development (in auspex-website/)
```bash
npm run dev              # Start Next.js development server
npm run build            # Build for current environment
npm run lint             # Run ESLint
npm run typecheck        # TypeScript type checking
```

### Environment-Specific Deployment
```bash
npm run build:staging    # Build with staging configuration
npm run build:prod       # Build with production configuration
npm run deploy:staging   # Deploy to staging environment
npm run deploy:prod      # Deploy to production environment
```

### Tools (in tools/)
```bash
# Audio processing pipeline
python convert_s3_codecs.py

# Video generation for YouTube
python create_youtube_video.py --audio-folder "path/to/audio" --image "cover.jpg"
```

## 📚 Documentation

- [Website Documentation](./auspex-website/README.md) - Complete tech stack, deployment, and development guide
- [Tools Documentation](./tools/README.md) - Automation scripts for audio processing and video creation
- [Terraform Documentation](./auspex-website/terraform/README.md) - Infrastructure setup and management

## 🎵 Features

- **Music Releases** - Browse releases with album art, streaming links, and multi-format downloads
- **Live Performances** - Watch recorded live sets with embedded YouTube player
- **Multi-Platform Integration** - Direct links to Spotify, Bandcamp, SoundCloud, Apple Music
- **Year-Based Filtering** - Browse releases by year with dynamic filtering
- **Responsive Design** - Mobile-first design with glass morphism effects
- **Fast Loading** - Global CDN with optimized static assets
- **Multi-Format Downloads** - MP3, FLAC, WAV, AAC, and more via S3

## 🔄 Deployment Workflow

1. **Make changes** to the codebase
2. **Test locally** with `npm run dev`
3. **Deploy to staging** with `npm run deploy:staging`
4. **Test staging** at https://stage.auspexrecords.com
5. **Deploy to production** with `npm run deploy:prod`
6. **Verify production** at https://auspexrecords.com

## 🛠️ Infrastructure Management

### Initial Setup
```bash
# Bootstrap Terraform state management
cd auspex-website/terraform/bootstrap
terraform init && terraform apply

# Deploy staging environment
cd ../environments/staging
terraform init && terraform apply

# Deploy production environment
cd ../environments/prod
terraform init && terraform apply
```

### Ongoing Management
- Use `npm run deploy:staging` and `npm run deploy:prod` for website updates
- Use Terraform directly for infrastructure changes
- State files are automatically managed in S3 with DynamoDB locking
- Modular structure allows independent updates to different components

## 🎵 Music Processing Workflow

### For New Releases
1. **Prepare high-quality WAV files** and album artwork
2. **Upload to preprocessing S3 bucket** using AWS CLI
3. **Run audio conversion tool**: `python tools/convert_s3_codecs.py`
4. **Generate YouTube videos**: `python tools/create_youtube_video.py`
5. **Update website data** in `auspex-website/src/lib/data.ts`
6. **Deploy to staging** for testing, then production

### Supported Audio Formats
The conversion pipeline generates: MP3 (320k, V0), FLAC, AAC, Ogg Vorbis, ALAC, WAV, AIFF

## 🤝 Contributing

1. **Website changes**: Work in `auspex-website/` directory
2. **Test locally**: `npm run dev` for development server
3. **Deploy to staging**: `npm run deploy:staging` for testing
4. **Deploy to production**: `npm run deploy:prod` when ready
5. **Tools development**: Work in `tools/` directory with Python virtual environment

## 🔧 Important Notes

- **Static Data**: Website uses TypeScript data files, no database required
- **S3 Integration**: Direct downloads from S3 buckets for all audio formats
- **Modular Infrastructure**: Terraform modules allow independent component updates
- **Environment Separation**: Complete isolation between staging and production
- **Automated Processing**: Tools handle audio conversion and video generation

## 📄 License

Private project for Auspex Records.
